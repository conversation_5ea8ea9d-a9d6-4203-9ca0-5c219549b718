"""
A.T.L.A.S. Real-Time Scanner Engine
Continuous monitoring of S&P 500 stocks for Lee Method pattern detection
Provides real-time updates via WebSocket and efficient API usage management
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import threading
import queue
import time as time_module

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import get_api_config, settings
from models import Quote, EngineStatus, TTMSqueezeSignal, SignalStrength
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols, get_high_volume_symbols
from atlas_lee_method import LeeMethodScanner
from atlas_market_core import AtlasMarketEngine

logger = logging.getLogger(__name__)

@dataclass
class ScannerResult:
    """Real-time scanner result"""
    symbol: str
    price: float
    change: float
    change_percent: float
    pattern_found: bool
    pattern_type: str
    confidence: float
    signal_strength: str
    timestamp: str
    histogram_current: float
    ema5_trend: bool
    ema8_trend: bool
    momentum_trend: bool
    squeeze_active: bool
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None

@dataclass
class ScannerConfig:
    """Scanner configuration"""
    enabled: bool = True
    scan_interval: int = 30  # seconds
    market_hours_only: bool = True
    symbol_filter: List[str] = None  # None = all S&P 500
    min_confidence: float = 0.6
    max_concurrent_scans: int = 10
    api_rate_limit: int = 100  # requests per minute
    require_squeeze: bool = False
    pattern_sensitivity: float = 0.7

class AtlasRealtimeScanner:
    """
    Continuous real-time scanner for Lee Method pattern detection
    Monitors all S&P 500 stocks during market hours with efficient API usage
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = ScannerConfig()
        self.lee_scanner = LeeMethodScanner()
        self.market_engine = AtlasMarketEngine()

        # Scanner state
        self.is_running = False
        self.is_market_hours = False
        self.scan_count = 0
        self.last_scan_time = None
        self.active_results: Dict[str, ScannerResult] = {}
        self.scan_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # Threading
        self.scanner_thread = None
        self.result_processor_thread = None
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_concurrent_scans)
        
        # API rate limiting
        self.api_calls_per_minute = 0
        self.last_api_reset = datetime.now()
        self.api_lock = threading.Lock()
        
        # WebSocket connections for real-time updates
        self.websocket_connections: Set = set()
        
        # Market hours (9:30 AM - 4:00 PM ET)
        self.market_open = time(9, 30)
        self.market_close = time(16, 0)
        
        self.logger.info("[OK] A.T.L.A.S. Real-Time Scanner initialized")

    async def initialize(self) -> bool:
        """Initialize the scanner engine"""
        try:
            # Initialize market engine first
            await self.market_engine.initialize()

            # Initialize Lee Method scanner
            await self.lee_scanner.initialize()

            # Load scanner configuration
            await self._load_scanner_config()

            # Get symbols to scan
            if self.config.symbol_filter:
                self.scan_symbols = self.config.symbol_filter
            else:
                # Use full S&P 500 for comprehensive scanning
                self.scan_symbols = get_sp500_symbols()

            self.logger.info(f"[OK] Scanner initialized with {len(self.scan_symbols)} symbols")
            return True

        except Exception as e:
            self.logger.error(f"Scanner initialization failed: {e}")
            return False

    async def start_scanner(self) -> bool:
        """Start the continuous real-time scanner"""
        try:
            if self.is_running:
                self.logger.warning("Scanner is already running")
                return True
            
            self.is_running = True
            
            # Start scanner thread
            self.scanner_thread = threading.Thread(target=self._scanner_loop, daemon=True)
            self.scanner_thread.start()
            
            # Start result processor thread
            self.result_processor_thread = threading.Thread(target=self._result_processor_loop, daemon=True)
            self.result_processor_thread.start()
            
            self.logger.info("[OK] Real-time scanner started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start scanner: {e}")
            self.is_running = False
            return False

    async def stop_scanner(self) -> bool:
        """Stop the continuous real-time scanner"""
        try:
            self.is_running = False
            
            # Wait for threads to finish
            if self.scanner_thread and self.scanner_thread.is_alive():
                self.scanner_thread.join(timeout=5)
            
            if self.result_processor_thread and self.result_processor_thread.is_alive():
                self.result_processor_thread.join(timeout=5)
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            self.logger.info("[OK] Real-time scanner stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping scanner: {e}")
            return False

    def _scanner_loop(self):
        """Main scanner loop running in background thread"""
        while self.is_running:
            try:
                # Check if we should scan (market hours, enabled, etc.)
                if not self._should_scan():
                    time_module.sleep(10)  # Check again in 10 seconds
                    continue
                
                # Perform scan cycle
                self._perform_scan_cycle()
                
                # Wait for next scan interval
                time_module.sleep(self.config.scan_interval)
                
            except Exception as e:
                self.logger.error(f"Error in scanner loop: {e}")
                time_module.sleep(30)  # Wait before retrying

    def _result_processor_loop(self):
        """Process scan results and send WebSocket updates"""
        while self.is_running:
            try:
                # Get result from queue (blocking with timeout)
                try:
                    result = self.result_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Process the result
                self._process_scan_result(result)
                
                # Send WebSocket update
                asyncio.run(self._send_websocket_update(result))
                
            except Exception as e:
                self.logger.error(f"Error processing results: {e}")

    def _should_scan(self) -> bool:
        """Check if scanner should perform a scan cycle"""
        if not self.config.enabled:
            return False
        
        # Check market hours if required
        if self.config.market_hours_only:
            current_time = datetime.now().time()
            self.is_market_hours = self.market_open <= current_time <= self.market_close
            if not self.is_market_hours:
                return False
        
        # Check API rate limits
        with self.api_lock:
            now = datetime.now()
            if (now - self.last_api_reset).total_seconds() >= 60:
                self.api_calls_per_minute = 0
                self.last_api_reset = now
            
            if self.api_calls_per_minute >= self.config.api_rate_limit:
                return False
        
        return True

    def _perform_scan_cycle(self):
        """Perform a complete scan cycle of all symbols"""
        try:
            self.scan_count += 1
            self.last_scan_time = datetime.now()
            
            # Batch symbols for efficient scanning
            batch_size = min(self.config.max_concurrent_scans, 20)
            symbol_batches = [self.scan_symbols[i:i + batch_size] 
                            for i in range(0, len(self.scan_symbols), batch_size)]
            
            # Submit scan tasks to executor
            futures = []
            for batch in symbol_batches:
                future = self.executor.submit(self._scan_symbol_batch, batch)
                futures.append(future)
            
            # Collect results
            for future in futures:
                try:
                    batch_results = future.result(timeout=30)
                    for result in batch_results:
                        if result:
                            self.result_queue.put(result)
                except Exception as e:
                    self.logger.error(f"Error getting batch results: {e}")
            
            self.logger.debug(f"Scan cycle {self.scan_count} completed")
            
        except Exception as e:
            self.logger.error(f"Error in scan cycle: {e}")

    def _scan_symbol_batch(self, symbols: List[str]) -> List[Optional[ScannerResult]]:
        """Scan a batch of symbols for Lee Method patterns"""
        results = []
        
        for symbol in symbols:
            try:
                result = self._scan_single_symbol(symbol)
                results.append(result)
                
                # Update API call counter
                with self.api_lock:
                    self.api_calls_per_minute += 1
                
            except Exception as e:
                self.logger.error(f"Error scanning {symbol}: {e}")
                results.append(None)
        
        return results

    def _scan_single_symbol(self, symbol: str) -> Optional[ScannerResult]:
        """Scan a single symbol for Lee Method pattern"""
        try:
            # This would be called synchronously in the thread
            # We need to run the async Lee Method scanner
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                signal = loop.run_until_complete(self.lee_scanner.scan_symbol(symbol))
                if not signal or signal.confidence < self.config.min_confidence:
                    return None
                
                # Get current market data
                market_data = loop.run_until_complete(self._get_market_data(symbol))
                if not market_data:
                    return None
                
                # Create scanner result
                result = ScannerResult(
                    symbol=symbol,
                    price=market_data.get('price', 0.0),
                    change=market_data.get('change', 0.0),
                    change_percent=market_data.get('change_percent', 0.0),
                    pattern_found=True,
                    pattern_type='lee_method_ttm_squeeze',
                    confidence=signal.confidence,
                    signal_strength=signal.strength.value if signal.strength else 'MODERATE',
                    timestamp=datetime.now().isoformat(),
                    histogram_current=signal.histogram_current,
                    ema5_trend=signal.ema5_uptrend,
                    ema8_trend=signal.ema8_uptrend,
                    momentum_trend=signal.momentum_uptrend,
                    squeeze_active=signal.squeeze_active,
                    target_price=signal.target_price,
                    stop_loss=signal.stop_loss
                )
                
                return result
                
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"Error scanning symbol {symbol}: {e}")
            return None

    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current market data for symbol with fallback mechanisms"""
        try:
            # Use the integrated market engine with multiple data sources
            quote = await self.market_engine.get_quote(symbol)
            if quote:
                return {
                    'price': quote.price,
                    'change': quote.change,
                    'change_percent': quote.change_percent,
                    'volume': quote.volume,
                    'timestamp': quote.timestamp.isoformat()
                }

            # Fallback: try to get basic data from FMP directly
            return await self._get_fmp_fallback_data(symbol)

        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {e}")
            return await self._get_fmp_fallback_data(symbol)

    async def _get_fmp_fallback_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fallback method to get basic market data from FMP API"""
        try:
            import aiohttp

            # Use FMP API key from settings
            fmp_api_key = getattr(settings, 'FMP_API_KEY', 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7')
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
            params = {"apikey": fmp_api_key}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and len(data) > 0:
                            quote_data = data[0]
                            return {
                                'price': float(quote_data.get('price', 0)),
                                'change': float(quote_data.get('change', 0)),
                                'change_percent': float(quote_data.get('changesPercentage', 0)),
                                'volume': int(quote_data.get('volume', 0)),
                                'timestamp': datetime.now().isoformat()
                            }

            return None

        except Exception as e:
            self.logger.error(f"FMP fallback error for {symbol}: {e}")
            return None

    def _process_scan_result(self, result: ScannerResult):
        """Process and store scan result"""
        try:
            # Update active results
            self.active_results[result.symbol] = result
            
            # Clean up old results (older than 1 hour)
            cutoff_time = datetime.now() - timedelta(hours=1)
            expired_symbols = []
            
            for symbol, stored_result in self.active_results.items():
                result_time = datetime.fromisoformat(stored_result.timestamp)
                if result_time < cutoff_time:
                    expired_symbols.append(symbol)
            
            for symbol in expired_symbols:
                del self.active_results[symbol]
                
        except Exception as e:
            self.logger.error(f"Error processing scan result: {e}")

    async def _send_websocket_update(self, result: ScannerResult):
        """Send real-time update to WebSocket connections"""
        try:
            if not self.websocket_connections:
                return
            
            update_data = {
                'type': 'scanner_update',
                'data': asdict(result)
            }
            
            # Send to all connected WebSocket clients
            disconnected = set()
            for websocket in self.websocket_connections:
                try:
                    await websocket.send(json.dumps(update_data))
                except Exception:
                    disconnected.add(websocket)
            
            # Remove disconnected clients
            self.websocket_connections -= disconnected
            
        except Exception as e:
            self.logger.error(f"Error sending WebSocket update: {e}")

    async def _load_scanner_config(self):
        """Load scanner configuration from settings"""
        try:
            # Load from settings or use defaults
            self.config.enabled = getattr(settings, 'SCANNER_ENABLED', True)
            self.config.scan_interval = getattr(settings, 'SCANNER_INTERVAL', 30)
            self.config.market_hours_only = getattr(settings, 'SCANNER_MARKET_HOURS_ONLY', True)
            self.config.min_confidence = getattr(settings, 'SCANNER_MIN_CONFIDENCE', 0.6)
            self.config.max_concurrent_scans = getattr(settings, 'SCANNER_MAX_CONCURRENT', 10)
            self.config.api_rate_limit = getattr(settings, 'SCANNER_API_RATE_LIMIT', 100)
            
        except Exception as e:
            self.logger.error(f"Error loading scanner config: {e}")

    # Public API methods
    
    async def get_active_results(self) -> List[Dict[str, Any]]:
        """Get current active scanner results"""
        try:
            results = []
            for result in self.active_results.values():
                results.append(asdict(result))
            
            # Sort by confidence descending
            results.sort(key=lambda x: x['confidence'], reverse=True)
            return results
            
        except Exception as e:
            self.logger.error(f"Error getting active results: {e}")
            return []

    async def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """Update scanner configuration"""
        try:
            for key, value in config_updates.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            self.logger.info(f"Scanner configuration updated: {config_updates}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating scanner config: {e}")
            return False

    def add_websocket_connection(self, websocket):
        """Add WebSocket connection for real-time updates"""
        self.websocket_connections.add(websocket)

    def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)

    def get_scanner_status(self) -> Dict[str, Any]:
        """Get current scanner status"""
        return {
            'running': self.is_running,
            'market_hours': self.is_market_hours,
            'scan_count': self.scan_count,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'active_results_count': len(self.active_results),
            'symbols_monitored': len(self.scan_symbols),
            'api_calls_per_minute': self.api_calls_per_minute,
            'config': asdict(self.config)
        }
